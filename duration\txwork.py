import requests

class txwork:
    def __init__(self):
        self.url = 'https://careers.tencent.com/tencentcareer/api/post/Query?timestamp=1755570025924&countryId&cityId&bgIds&productId&categoryId&parentCategoryId&attrId&keyword=python&pageIndex=1&pageSize=100&language=zh-cn&area=cn'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        }
    def get(self, params=None, headers=None):
        response = requests.get(self.url, params=params, headers=headers)
        return response.json()

if __name__ == '__main__':
    resp = txwork().get()
    print(resp['Data']['Count'])