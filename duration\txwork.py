import sys
import os
import requests

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


from utils.db_utils import MysqlUtil

class txwork:
    def __init__(self):
        self.url = 'https://careers.tencent.com/tencentcareer/api/post/Query?timestamp=1755570025924&countryId&cityId&bgIds&productId&categoryId&parentCategoryId&attrId&language=zh-cn&area=cn'
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36'
        }
    def get(self, params=None, headers=None):
        params = {
            'keyword': 'python',
            'pageIndex': 1,
            'pageSize': 10
        }
        response = requests.get(self.url, params=params, headers=headers)
        return response.json()
    def parse_job_data(self, resp):
        job_data = resp['Data']['Posts']
        for job in job_data:
            item = dict()
            item['job_name'] = job['RecruitPostName']
            item['post_url'] = job['PostURL']
            item['description'] = job['Responsibility']
            item['city_name'] = job['CountryName']
            item['location_name'] = job['LocationName']
            self.save_job_data(item)
    def create_table(self):
        mysql_util = MysqlUtil()
        mysql_util.connect()
        sql = 'create table if not exists txwork (job_name varchar(255), post_url varchar(255), description varchar(255), city_name varchar(255), location_name varchar(255))'
        mysql_util.execute(sql)
        mysql_util.close()

    def save_job_data(self, item):
        mysql_util = MysqlUtil()
        mysql_util.connect()
        sql = 'insert into txwork (job_name, post_url, description, city_name, location_name) values (%s, %s, %s, %s, %s)'
        mysql_util.execute(sql, (item['job_name'], item['post_url'], item['description'], item['city_name'], item['location_name']))
        mysql_util.close()

if __name__ == '__main__':
    txwork = txwork()
    txwork.create_table()
    resp = txwork.get()
    print(resp['Data']['Count'])
    # txwork.parse_job_data(resp)