import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pymysql
from const.db_conf import *

class MysqlUtil:
    def __init__(self, host=mysql_host, user=mysql_user, password=mysql_password, database=mysql_db):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        self.cursor = None

    def connect(self):
        self.connection = pymysql.connect(
            host=self.host,
            user=self.user,
            password=self.password,
            database=self.database
        )
        self.cursor = self.connection.cursor()

    def close(self):
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()

    def execute(self, sql, params=None):
        self.cursor.execute(sql, params)
        self.connection.commit()

    def fetchall(self, sql, params=None):
        self.cursor.execute(sql, params)
        return self.cursor.fetchall()
    

if __name__ == '__main__':
    mysql_util = MysqlUtil()
    mysql_util.connect()
    sql = 'select * from user'
    print(mysql_util.fetchall(sql))
    mysql_util.close()